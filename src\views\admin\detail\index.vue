<script setup>
import SimplyBreadcrumb from "@/views/components/SimplyBreadcrumb/index.vue";
import { UploadFilled, Close } from "@element-plus/icons-vue";
import { ElMessage } from "element-plus";
import { getCosConfig } from "@/utils/cos"
import { complaintDetailApi, cityDistributionApi, complaintReplyApi } from '@/apis/complaint';

import imageIcon from "@/assets/images/detail/image.png";
import wordIcon from "@/assets/images/detail/word.png";
import pdfIcon from "@/assets/images/detail/pdf.png";

// 获取cos配置
const cos = ref()
const cosConfig = ref({})
const getCosConfigFn = async () => {
  const res = await getCosConfig()
  if (res) {
    const { cosInstance, config } = res
    cos.value = cosInstance
    cosConfig.value = config
  }
}

// 如果需要路由功能，可以取消注释
const route = useRoute()
const router = useRouter()

const type = route.query.type

// 模拟数据
const complaintData = ref({
  // 投诉人信息
  complainant: {
    name: "张三",
    phone: "138****5678",
    identity: "学生家长",
    gender: "男",
  },
  // 投诉学校信息
  school: {
    region: "江苏省南京市玄武区",
    schoolName: "南京市第一中学",
    schoolType: "中学",
  },
  // 具体问题
  problem: {
    reportTime: "2025.07.15 14:30:22",
    category: "膳食经费",
    description:
      "举报南京市第一中学膳食经费有问题，小学部膳食质量差，请有关部门介入调查，请相关部门严查。",
    attachments: [
      { name: "举报校内问题片1.jpg", url: "#", type: "img" },
      { name: "举报校内问题片2.jpg", url: "#", type: "img" },
    ],
  },
  // 分办信息
  assignment: {
    assignTime: "2025.07.15 14:30:22",
    assignedTo: "请及时处理该问题，并确保内部高效处理。",
  },
  // 回复信息
  replies: [
    {
      id: 1,
      department: "鼓楼区教育局",
      replyTime: "2025-07-18 16:45:12",
      handlerName: "李四",
      handlerPhone: "13370898798",
      content: "接到投诉后，我们立即组织人员对该区域进行了清理，并加强了厨房环境卫生管理。今后加强食堂卫生管理，确保类似问题不再发生。",
      attachments: [
        { name: "处理后相关照片1.jpg", url: "#", type: "img" },
        { name: "处理情况说明.doc", url: "#", type: "doc" },
        { name: "处理情况说明.doc", url: "#", type: "doc" }
      ]
    }
  ],
  // 审核信息
  reviews: [
    {
      id: 1,
      status: "未通过",
      reviewer: "审核时间：2025-07-20 09:15:30",
      content: "请继续跟进处理，但只是表面处理，没有从根本上解决问题。",
      statusColor: "#FF6B6B"
    }
  ],
  // 审核回复
  reviewReplies: [
    {
      id: 1,
      department: "鼓楼区教育局",
      replyTime: "2025-07-18 16:45:12",
      handlerName: "李四",
      handlerPhone: "13370898798",
      content: "接到投诉后，我们立即组织人员对该区域进行了清理，并加强了厨房环境卫生管理。今后加强食堂卫生管理，确保类似问题不再发生。",
      attachments: [
        { name: "处理后相关照片1.jpg", url: "#", type: "img" },
        { name: "处理情况说明.doc", url: "#", type: "doc" },
        { name: "处理情况说明.doc", url: "#", type: "doc" }
      ]
    }
  ],
  // 最终审核
  finalReview: {
    status: "已通过",
    reviewer: "审核时间：2025-07-20 09:15:30",
    statusColor: "#52C41A"
  },
  // 省厅督办意见
  supervisionOpinion: {
    opinion: "请及时处理该校问题，并反馈处理结果。",
    replyTime: "2025-07-18 16:45:12",
    attachments: [
      { name: "处理后相关照片1.jpg", url: "#", type: "img" },
      { name: "处理情况说明.doc", url: "#", type: "doc" },
      { name: "处理情况说明.doc", url: "#", type: "doc" }
    ]
  }
});

// 获取详情数据
const getDetail = async () => {
  try {
    const res = await complaintDetailApi(route.query.complaintId)
    if (res) {
      complaintData.value = res.data

      if (complaintData.value.files) {
        complaintData.value.files.forEach(file => {
          file.type = getFileType(file.name)
        })
      }
    }
  } catch (err) {
    console.log('获取详情错误', err)
  }
}


// 分办表单数据
const assignmentForm = ref({
  assignmentUnit: '',
  assignmentOpinion: ''
});

// 分办表单验证规则
const assignmentRules = {
  assignmentUnit: [
    { required: true, message: '请选择分办单位', trigger: 'change' }
  ],
  assignmentOpinion: [
    { required: true, message: '请输入分办意见', trigger: 'blur' },
    { max: 100, message: '分办意见不能超过100个字符', trigger: 'blur' }
  ]
};

// 分办单位选项
const assignmentUnitOptions = [
  { label: '南京市教育局', value: 'nanjing_education' },
  { label: '苏州市教育局', value: 'suzhou_education' },
  { label: '无锡市教育局', value: 'wuxi_education' },
  { label: '常州市教育局', value: 'changzhou_education' },
  { label: '南通市教育局', value: 'nantong_education' },
  { label: '连云港市教育局', value: 'lianyungang_education' },
  { label: '淮安市教育局', value: 'huaian_education' },
  { label: '盐城市教育局', value: 'yancheng_education' },
  { label: '扬州市教育局', value: 'yangzhou_education' },
  { label: '镇江市教育局', value: 'zhenjiang_education' },
  { label: '泰州市教育局', value: 'taizhou_education' },
  { label: '宿迁市教育局', value: 'suqian_education' },
  { label: '徐州市教育局', value: 'xuzhou_education' }
];

// 审核表单数据
const reviewForm = ref({
  status: '',
  reason: ''
});

// 审核表单验证规则
const reviewRules = computed(() => ({
  status: [
    { required: true, message: '请选择审核状态', trigger: 'change' }
  ],
  reason: reviewForm.value.status === '未通过' ? [
    { required: true, message: '请输入不通过原因', trigger: 'blur' },
    { max: 50, message: '不通过原因不能超过50个字符', trigger: 'blur' }
  ] : []
}));

// 审核状态选项
const reviewStatusOptions = [
  { label: '通过', value: '已通过' },
  { label: '不通过', value: '未通过' }
];

// 回复表单数据
const replyForm = ref({
  content: '',
  handlerName: '',
  handlerPhone: '',
  attachments: []
});

// 表单验证规则
const replyRules = {
  content: [
    { required: true, message: '请输入回复内容', trigger: 'blur' }
  ],
  handlerName: [
    { required: true, message: '请输入经办人姓名', trigger: 'blur' }
  ],
  handlerPhone: [
    { required: true, message: '请输入经办人电话', trigger: 'blur' },
    { pattern: /^1[3-9]\d{9}$/, message: '请输入正确的手机号码', trigger: 'blur' }
  ]
};

// 文件上传相关
const fileInputRef = ref();
const fileList = ref([]);
const isDragOver = ref(false);

// 支持的文件格式
const allowedFormats = ['jpg', 'jpeg', 'png', 'doc', 'docx', 'pdf'];

// 文件验证
const validateFile = (file) => {
  const fileExtension = file.name.split('.').pop().toLowerCase();

  if (!allowedFormats.includes(fileExtension)) {
    ElMessage.error('只支持 jpg/doc/docx/pdf/png 格式的文件');
    return false;
  }

  if (fileList.value.length >= 5) {
    ElMessage.error('最多只能上传5个文件');
    return false;
  }

  const isLt10M = file.size / 1024 / 1024 < 10;
  if (!isLt10M) {
    ElMessage.error('文件大小不能超过 10MB');
    return false;
  }

  return true;
};

// 处理文件选择
const handleFileSelect = (files) => {
  const validFiles = Array.from(files).filter(validateFile);

  validFiles.forEach(file => {
    const fileItem = {
      id: Date.now() + Math.random(),
      name: file.name,
      size: file.size,
      type: getFileType(file.name),
      file: file,
      url: URL.createObjectURL(file)
    };

    fileList.value.push(fileItem);
  });

  replyForm.value.attachments = [...fileList.value];
};

// 点击上传区域
const handleUploadClick = () => {
  fileInputRef.value?.click();
};

// 文件输入变化
const handleFileInputChange = (event) => {
  const files = event.target.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
  // 清空input值，允许重复选择同一文件
  event.target.value = '';
};

// 拖拽相关事件
const handleDragOver = (event) => {
  event.preventDefault();
  isDragOver.value = true;
};

const handleDragLeave = (event) => {
  event.preventDefault();
  isDragOver.value = false;
};

const handleDrop = (event) => {
  event.preventDefault();
  isDragOver.value = false;

  const files = event.dataTransfer.files;
  if (files && files.length > 0) {
    handleFileSelect(files);
  }
};

// 删除文件
const removeFile = (fileId) => {
  const index = fileList.value.findIndex(file => file.id === fileId);
  if (index > -1) {
    // 释放URL对象
    if (fileList.value[index].url.startsWith('blob:')) {
      URL.revokeObjectURL(fileList.value[index].url);
    }
    fileList.value.splice(index, 1);
    replyForm.value.attachments = [...fileList.value];
  }
};

// 获取文件类型
const getFileType = (fileName) => {
  const extension = fileName.split('.').pop().toLowerCase();
  if (['jpg', 'jpeg', 'png'].includes(extension)) return 'img';
  if (['doc', 'docx'].includes(extension)) return 'doc';
  if (extension === 'pdf') return 'pdf';
  return 'doc';
};

// 格式化文件大小
const formatFileSize = (bytes) => {
  if (bytes === 0) return '0 B';
  const k = 1024;
  const sizes = ['B', 'KB', 'MB', 'GB'];
  const i = Math.floor(Math.log(bytes) / Math.log(k));
  return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
};

// 提交回复
const submitReply = async () => {
  try {
    await replyFormRef.value.validate();

    // 创建FormData用于文件上传
    const formData = new FormData();
    formData.append('content', replyForm.value.content);
    formData.append('handlerName', replyForm.value.handlerName);
    formData.append('handlerPhone', replyForm.value.handlerPhone);

    // 添加文件
    fileList.value.forEach((fileItem, index) => {
      formData.append(`attachments[${index}]`, fileItem.file);
    });

    // 这里实现提交逻辑
    console.log('提交回复:', {
      content: replyForm.value.content,
      handlerName: replyForm.value.handlerName,
      handlerPhone: replyForm.value.handlerPhone,
      attachments: fileList.value.map(f => ({ name: f.name, size: f.size, type: f.type }))
    });

    ElMessage.success('回复提交成功');

    // 重置表单
    replyForm.value = {
      content: '',
      handlerName: '',
      handlerPhone: '',
      attachments: []
    };

    // 清理文件列表和URL对象
    fileList.value.forEach(file => {
      if (file.url.startsWith('blob:')) {
        URL.revokeObjectURL(file.url);
      }
    });
    fileList.value = [];

  } catch (error) {
    console.error('表单验证失败:', error);
  }
};

// 保存草稿
const saveDraft = () => {
  console.log('保存草稿:', replyForm.value);
  ElMessage.success('草稿保存成功');
};

// 表单引用
const assignmentFormRef = ref();
const reviewFormRef = ref();
const replyFormRef = ref();

// 提交分办
const submitAssignment = async () => {
  try {
    await assignmentFormRef.value.validate();

    // 这里实现分办提交逻辑
    console.log('提交分办:', assignmentForm.value);
    ElMessage.success('分办提交成功');

    // 重置表单
    assignmentForm.value = {
      assignmentUnit: '',
      assignmentOpinion: ''
    };

  } catch (error) {
    console.error('分办表单验证失败:', error);
  }
};

// 审核状态变化处理
const handleReviewStatusChange = (value) => {
  if (value === '已通过') {
    // 如果选择通过，清空不通过原因
    reviewForm.value.reason = '';
  }
};

// 提交审核
const submitReview = async () => {
  try {
    await reviewFormRef.value.validate();

    // 这里实现审核提交逻辑
    console.log('提交审核:', reviewForm.value);
    ElMessage.success('审核提交成功');

    // 重置表单
    reviewForm.value = {
      status: '',
      reason: ''
    };

  } catch (error) {
    console.error('审核表单验证失败:', error);
  }
};

// 下载附件
const downloadAttachment = (attachment) => {
  console.log("下载附件:", attachment.name);
  // 这里实现下载逻辑
};

onMounted(() => {
  getDetail()
})
</script>

<template>
  <div class="detail app-container">
    <SimplyBreadcrumb />
    <section class="content px-[24px] py-[20px] bg-[#fff] rounded-[10px]">
      <!-- 投诉人信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img src="@/assets/images/detail/person.png" alt="" width="12" height="14" />
          <span class="title-text">投诉人信息</span>
        </div>
        <div class="info-grid">
          <div class="info-item">
            <span class="label">姓名</span>
            <span class="value">{{ complaintData.userTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">手机</span>
            <span class="value">{{ complaintData.userMobile }}</span>
          </div>
          <div class="info-item">
            <span class="label">身份</span>
            <span class="value">{{ complaintData.identity }}</span>
          </div>
          <div class="info-item">
            <span class="label">性别</span>
            <span class="value">{{ complaintData.sex === 1 ? '男' : complaintData.sex === 2 ? '女' : '-' }}</span>
          </div>
        </div>
      </div>

      <!-- 投诉学校信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img src="@/assets/images/detail/school.png" alt="" width="12" height="14" />
          <span class="title-text">投诉学校信息</span>
        </div>
        <div class="info-grid grid-3">
          <div class="info-item">
            <span class="label">地区</span>
            <span class="value">{{ `江苏省${complaintData.cityTitle}${complaintData.areaTitle === '直属学校' ? '' : complaintData.areaTitle}` }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校</span>
            <span class="value">{{ complaintData.schoolTitle }}</span>
          </div>
          <div class="info-item">
            <span class="label">学校类型</span>
            <span class="value">{{ complaintData.stageTitle }}</span>
          </div>
        </div>
      </div>

      <!-- 具体问题 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img src="@/assets/images/detail/question.png" alt="" width="12" height="12" />
          <span class="title-text">具体问题</span>
        </div>
        <div class="problem-content">
          <div class="info-grid grid-2 mb-[20px]">
            <div class="info-item">
              <span class="label">投诉时间</span>
              <span class="value">{{ complaintData.createAt }}</span>
            </div>
            <div class="info-item">
              <span class="label">分类</span>
              <span class="value">{{ complaintData.complaintTypeTitle }}</span>
            </div>
          </div>

          <div class="problem-description mb-[20px]">
            <div class="description-text">
              <div class="label mb-[8px]">举报内容</div>
              {{ complaintData.content }}
            </div>
          </div>

          <div class="attachments">
            <div class="label mb-[12px]">问题附件</div>
            <div class="attachment-list">
              <div v-for="(attachment, index) in complaintData?.files" :key="index" class="attachment-item"
                @click="downloadAttachment(attachment)">
                <img class="attachment-icon" :src="attachment.type === 'img'
                    ? imageIcon
                    : attachment.type === 'pdf'
                      ? pdfIcon
                      : attachment.type === 'word' || attachment.type === 'doc'
                        ? wordIcon
                        : ''
                  " />
                <span class="attachment-name">{{ attachment.name }}</span>
                <img class="download-icon" src="@/assets/images/detail/download.png" width="15" height="14" />
              </div>
            </div>
          </div>
        </div>
      </div>

      <!-- 分办信息 -->
      <div class="info-section mb-[20px]">
        <div class="section-title">
          <img src="@/assets/images/detail/supervise-info.png" alt="" width="14" height="14" />
          <span class="title-text">分办信息</span>
        </div>
        <div class="assignment-content">
          <div class="assignment-description">
            <div class="label mb-[8px]">分办意见</div>
            <div class="description-text">
              {{ complaintData?.review }}
            </div>
          </div>
        </div>
      </div>

      <!-- 回复信息 -->
      <template v-for="item in complaintData.complaintLogs" :key="item.complaintId">
        <div class="info-section mb-[20px]">
          <div class="section-title">
            <img src="@/assets/images/detail/answer-info.png" alt="" width="14" height="11" />
            <span class="title-text">回复信息</span>
          </div>
          <div class="reply-content">
            <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
            <div class="info-grid grid-2 mb-[20px]">
              <div class="info-item no-padding-bg">
                <span class="label">办理单位</span>
                <span class="value">{{ reply.department }}</span>
              </div>
              <div class="info-item no-padding-bg">
                <span class="label">回复时间</span>
                <span class="value">{{ reply.replyTime }}</span>
              </div>
              <div class="info-item no-padding-bg">
                <span class="label">经办人姓名</span>
                <span class="value">{{ reply.handlerName }}</span>
              </div>
              <div class="info-item no-padding-bg">
                <span class="label">经办人电话</span>
                <span class="value">{{ reply.handlerPhone }}</span>
              </div>
            </div>

            <!-- 回复内容单独一行 -->
            <div class="reply-description mb-[20px]">
              <div class="label mb-[8px]">回复内容</div>
              <div class="description-text no-padding-bg">{{ reply.content }}</div>
            </div>

            <!-- 回复附件单独一行 -->
            <div class="attachments" v-if="reply.attachments && reply.attachments.length > 0">
              <div class="label mb-[12px]">回复附件</div>
              <div class="attachment-list">
                <div v-for="(attachment, attachIndex) in reply.attachments" :key="attachIndex" class="attachment-item"
                  @click="complaintLogs(attachment)">
                  <img class="attachment-icon" :src="attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                        ? pdfIcon
                        : attachment.type === 'word' || attachment.type === 'doc'
                          ? wordIcon
                          : ''
                    " />
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <img class="download-icon" src="@/assets/images/detail/download.png" width="15" height="14" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 审核信息 -->
        <div class="info-section mb-[20px]">
          <div class="section-title">
            <img src="@/assets/images/detail/review-info.png" alt="" width="14" height="14" />
            <span class="title-text">审核信息</span>
          </div>
          <div class="review-content">
            <div class="review-status mb-[5px]">
              <span class="status-label" :class="item.status === '已通过' ? 'status-passed' : 'status-failed'">{{
                review.status }}</span>
              <span class="review-time">{{ item.reviewer }}</span>
            </div>

            <div class="review-description" v-if="review.content">
              <div class="description-text">{{ item.content }}</div>
            </div>
          </div>
        </div>

        <!-- 审核回复 -->
        <div class="info-section mb-[20px]">
          <div class="section-title">
            <img src="@/assets/images/detail/review-answer.png" alt="" width="14" height="14" />
            <span class="title-text">审核回复</span>
          </div>
          <div class="reply-content">
            <!-- 两行两列布局：办理单位、回复时间、经办人姓名、经办人电话 -->
            <div class="info-grid grid-2 mb-[20px]">
              <div class="info-item no-padding-bg">
                <span class="label">办理单位</span>
                <span class="value">{{ item.department }}</span>
              </div>
              <div class="info-item no-padding-bg">
                <span class="label">回复时间</span>
                <span class="value">{{ item.replyTime }}</span>
              </div>
            </div>

            <!-- 回复内容单独一行 -->
            <div class="reply-description mb-[20px]">
              <div class="label mb-[8px]">回复内容</div>
              <div class="description-text no-padding-bg">{{ reviewReply.content }}</div>
            </div>

            <!-- 回复附件单独一行 -->
            <div class="attachments" v-if="reviewReply.attachments && reviewReply.attachments.length > 0">
              <div class="label mb-[12px]">回复附件</div>
              <div class="attachment-list">
                <div v-for="(attachment, attachIndex) in reviewReply.attachments" :key="attachIndex"
                  class="attachment-item" @click="downloadAttachment(attachment)">
                  <img class="attachment-icon" :src="attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                        ? pdfIcon
                        : attachment.type === 'word' || attachment.type === 'doc'
                          ? wordIcon
                          : ''
                    " />
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <img class="download-icon" src="@/assets/images/detail/download.png" width="15" height="14" />
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 省厅督办意见 -->
        <div class="info-section mb-[20px]">
          <div class="section-title">
            <img src="@/assets/images/detail/supervise-advice.png" alt="" width="14" height="14" />
            <span class="title-text">省厅督办意见</span>
          </div>
          <div class="supervision-content">
            <!-- 督办意见与回复时间占一行 -->
            <div class="supervision-header mb-[20px]">
              <div class="supervision-opinion">
                <div class="label mb-[8px]">督办意见</div>
                <div class="description-text">{{ item.opinion }}</div>
              </div>
              <div class="supervision-time">
                <div class="label mb-[8px]">回复时间</div>
                <div class="time-text">{{ item.replyTime }}</div>
              </div>
            </div>

            <!-- 督办附件占一行 -->
            <div class="attachments" v-if="complaintData.supervisionOpinion.attachments && complaintData.supervisionOpinion.attachments.length > 0">
              <div class="label mb-[12px]">督办附件</div>
              <div class="attachment-list">
                <div v-for="(attachment, attachIndex) in complaintData.supervisionOpinion.attachments" :key="attachIndex"
                  class="attachment-item" @click="downloadAttachment(attachment)">
                  <img class="attachment-icon" :src="attachment.type === 'img'
                      ? imageIcon
                      : attachment.type === 'pdf'
                        ? pdfIcon
                        : attachment.type === 'word' || attachment.type === 'doc'
                          ? wordIcon
                          : ''
                    " />
                  <span class="attachment-name">{{ attachment.name }}</span>
                  <img class="download-icon" src="@/assets/images/detail/download.png" width="15" height="14" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </template>


      <!-- 分办表单 -->
      <div class="info-section mb-[20px]" v-if="type === 'distribution'">
        <div class="section-title">
          <img src="@/assets/images/detail/supervise-info.png" alt="" width="14" height="14" />
          <span class="title-text">分办信息</span>
        </div>
        <div class="assignment-form-content">
          <el-form ref="assignmentFormRef" :model="assignmentForm" :rules="assignmentRules" label-position="top">
            <!-- 分办单位 -->
            <el-form-item label="分办单位" prop="assignmentUnit" class="mb-[20px]">
              <el-select v-model="assignmentForm.assignmentUnit" placeholder="请选择分办单位" class="assignment-unit-select">
                <el-option v-for="option in assignmentUnitOptions" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>

            <!-- 分办意见 -->
            <el-form-item prop="assignmentOpinion" class="mb-[20px]">
              <template #label>
                <span class="assignment-opinion-label">
                  分办意见
                  <span class="assignment-opinion-hint">（不超过100字）</span>
                </span>
              </template>
              <el-input v-model="assignmentForm.assignmentOpinion" type="textarea" :rows="4"
                placeholder="请输入分办意见..." maxlength="100" show-word-limit class="assignment-opinion-input" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 审核表单 -->
      <div class="info-section mb-[20px]" v-if="type === 'review'">
        <div class="section-title">
          <img src="@/assets/images/detail/review-info.png" alt="" width="14" height="14" />
          <span class="title-text">审核信息</span>
        </div>
        <div class="review-form-content">
          <el-form ref="reviewFormRef" :model="reviewForm" :rules="reviewRules" label-width="120px"
            label-position="top">
            <!-- 审核状态 -->
            <el-form-item label="审核状态" prop="status" class="mb-[20px]">
              <el-select v-model="reviewForm.status" placeholder="请选择审核状态" @change="handleReviewStatusChange"
                class="review-status-select">
                <el-option v-for="option in reviewStatusOptions" :key="option.value" :label="option.label"
                  :value="option.value" />
              </el-select>
            </el-form-item>

            <!-- 不通过原因 -->
            <el-form-item v-if="reviewForm.status === '未通过'" prop="reason" class="mb-[20px]">
              <template #label>
                <span class="reason-label">
                  不通过原因
                  <span class="reason-hint">（不超过50字）</span>
                </span>
              </template>
              <el-input v-model="reviewForm.reason" placeholder="请输入不通过原因..." maxlength="50" show-word-limit
                class="reason-input" />
            </el-form-item>
          </el-form>
        </div>
      </div>

      <!-- 回复信息表单 -->
      <div class="info-section mb-[20px]" v-if="type === 'reply' || type === 'add-supervise'">
        <div class="section-title">
          <img v-if="type === 'add-supervise'" src="@/assets/images/detail/supervise-advice.png" alt="" width="14" height="14" />
          <img v-else src="@/assets/images/detail/answer-info.png" alt="" width="14" height="11" />
          <span class="title-text">{{ type === 'add-supervise' ? '省厅督办意见' : '回复信息' }}</span>
        </div>
        <div class="reply-form-content">
          <el-form ref="replyFormRef" :model="replyForm" :rules="replyRules" label-position="top">
            <!-- 回复内容 -->
            <el-form-item :label="type === 'add-supervise' ? '督办意见' : '回复信息'" prop="content" class="mb-[20px]">
              <el-input v-model="replyForm.content" type="textarea" :rows="6" :placeholder="`请输入详细的${type === 'add-supervise' ? '督办意见' : '回复信息'}...`"
                maxlength="1000" show-word-limit class="reply-textarea" />
            </el-form-item>

            <!-- 回复附件 -->
            <el-form-item class="mb-[20px]">
              <template #label>
                <span class="attachment-label">
                  {{ type === 'add-supervise' ? '督办附件' : '回复附件' }}
                  <span class="attachment-hint">（不超过5个，支持jpg/doc/pdf/png）</span>
                </span>
              </template>
              <div class="upload-area">
                <!-- 隐藏的文件输入 -->
                <input ref="fileInputRef" type="file" multiple accept=".jpg,.jpeg,.png,.doc,.docx,.pdf"
                  style="display: none" @change="handleFileInputChange" />

                <!-- 自定义上传区域 -->
                <div class="custom-upload-dragger" :class="{ 'drag-over': isDragOver }" @click="handleUploadClick"
                  @dragover="handleDragOver" @dragleave="handleDragLeave" @drop="handleDrop">
                  <div class="upload-content">
                    <el-icon color="#1A68A8" size="48">
                      <UploadFilled />
                    </el-icon>
                    <div class="upload-text">点击或拖拽文件到此处上传</div>
                    <div class="upload-hint">支持 jpg、doc、pdf、png格式，最多上传5个文件</div>
                  </div>
                </div>

                <!-- 文件列表 -->
                <div class="file-list" v-if="fileList.length > 0">
                  <div v-for="file in fileList" :key="file.id" class="file-item">
                    <img class="file-icon" :src="file.type === 'img'
                        ? imageIcon
                        : file.type === 'pdf'
                          ? pdfIcon
                          : wordIcon
                      " />
                    <span class="file-name">{{ file.name }}</span>
                    <span class="file-size">({{ formatFileSize(file.size) }})</span>
                    <el-icon class="remove-icon" @click="removeFile(file.id)">
                      <Close />
                    </el-icon>
                  </div>
                </div>
              </div>
            </el-form-item>

            <!-- 经办人信息 -->
            <div class="info-grid grid-2" v-if="type !== 'add-supervise'">
              <el-form-item label="经办人姓名" prop="handlerName">
                <el-input v-model="replyForm.handlerName" placeholder="请输入经办人姓名" class="handler-input" />
              </el-form-item>
              <el-form-item label="经办人电话" prop="handlerPhone">
                <el-input v-model="replyForm.handlerPhone" placeholder="请输入经办人电话" class="handler-input" />
              </el-form-item>
            </div>
          </el-form>
        </div>
      </div>

    </section>
    <footer class="flex-end-center gap-[8px] mt-[24px]">
      <el-button class="common-button-6" @click="router.back()">
        <template #icon>
          <img src="@/assets/images/detail/back.png" alt="" width="14" height="12">
        </template>
        返回
      </el-button>

      <!-- 分办页面按钮 -->
      <template v-if="type === 'distribution'">
        <el-button class="common-button-6" @click="saveDraft">
          <template #icon>
            <img src="@/assets/images/detail/save.png" alt="" width="14" height="14">
          </template>
          保存
        </el-button>
        <el-button class="common-button-7" @click="submitAssignment">
          <template #icon>
            <img src="@/assets/images/detail/submit.png" alt="" width="15" height="14">
          </template>
          提交
        </el-button>
      </template>

      <!-- 审核页面按钮 -->
      <template v-if="type === 'review'">
        <el-button class="common-button-6" @click="saveDraft">
          <template #icon>
            <img src="@/assets/images/detail/save.png" alt="" width="14" height="14">
          </template>
          保存
        </el-button>
        <el-button class="common-button-7" @click="submitReview">
          <template #icon>
            <img src="@/assets/images/detail/submit.png" alt="" width="15" height="14">
          </template>
          提交
        </el-button>
      </template>

      <!-- 回复页面按钮 -->
      <template v-if="type === 'reply'">
        <el-button class="common-button-6" @click="saveDraft">
          <template #icon>
            <img src="@/assets/images/detail/save.png" alt="" width="14" height="14">
          </template>
          保存
        </el-button>
        <el-button class="common-button-7" @click="submitReply">
          <template #icon>
            <img src="@/assets/images/detail/submit.png" alt="" width="15" height="14">
          </template>
          提交
        </el-button>
      </template>
    </footer>
  </div>
</template>

<style lang="scss" scoped>
.detail {
  .content {
    .info-section {
      .section-title {
        display: flex;
        align-items: center;
        margin-bottom: 14px;

        .title-icon {
          width: 4px;
          height: 20px;
          background: linear-gradient(180deg, #0ec3ed 0%, #239dde 100%);
          border-radius: 2px;
          margin-right: 12px;
        }

        .title-text {
          font-size: 14px;
          line-height: 20px;
          font-weight: 600;
          color: #2b2c33;
          padding-left: 6px;
        }
      }

      .info-grid {
        display: grid;
        grid-template-columns: repeat(4, 1fr);
        gap: 12px;

        &.grid-3 {
          grid-template-columns: repeat(3, 1fr);
          gap: 18px;
        }

        &.grid-2 {
          grid-template-columns: repeat(2, 1fr);
          gap: 12px;
        }

        .info-item {
          display: flex;
          flex-direction: column;
          align-items: flex-start;
          padding: 10px 16px;
          background: #fafafa;
          border-radius: 8px 8px 8px 8px;
          border: 1px solid #e2e3e6;

          &.no-padding-bg {
            padding: 0;
            background: transparent;
            border: none;
            border-radius: 0;
          }

          .label {
            font-size: 14px;
            line-height: 20px;
            font-weight: 400;
            color: #94959c;
            margin-bottom: 4px;
          }

          .value {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      .problem-content {
        .problem-description {
          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .description-text {
            font-size: 16px;
            color: #2b2c33;
            line-height: 24px;
            padding: 16px;
            background: #f8f9fa;
            border-radius: 8px;
            border: 1px solid #e2e3e6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          padding: 12px 16px;
          background: #f8f9fa;
          border: 1px solid #e2e3e6;
          border-radius: 8px;

          .label {
            font-size: 14px;
            font-weight: 400;
            color: #94959c;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      .assignment-content {
        .assignment-description {
          padding: 16px;
          background: #f8f9fa;
          border-radius: 8px;
          border: 1px solid #e2e3e6;

          &.no-padding-bg {
            padding: 0;
            background: transparent;
            border: none;
            border-radius: 0;
          }

          .label {
            font-size: 14px;
            color: #6d6f75;
          }

          .description-text {
            font-size: 16px;
            line-height: 24px;
            color: #2b2c33;
          }
        }
      }

      // 回复信息和审核回复样式
      .reply-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .reply-description {
          .label {
            font-size: 14px;
            color: #6D6F75;
          }

          .description-text {
            font-size: 16px;
            color: #2B2C33;
            line-height: 24px;
            padding: 16px;
            background: #ffffff;
            border-radius: 8px;
            border: 1px solid #E2E3E6;

            &.no-padding-bg {
              padding: 0;
              background: transparent;
              border: none;
              border-radius: 0;
            }
          }
        }

        .attachments {
          .label {
            font-size: 14px;
            color: #6D6F75;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      // 审核信息样式
      .review-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .review-status {
          display: flex;
          align-items: center;
          gap: 16px;

          .status-label {
            font-size: 12px;
            height: 20px;
            line-height: 20px;
            padding: 4px 8px;
            border-radius: 10px;
            display: inline-flex;
            align-items: center;

            &.status-passed {
              color: #28B28B;
              background-color: #DCFCE7;
            }

            &.status-failed {
              color: #E72C4A;
              background-color: #FFE9E9;
            }
          }

          .review-time {
            font-size: 14px;
            color: #6D6F75;
          }
        }

        .review-description {
          .description-text {
            font-size: 16px;
            color: #2B2C33;
            line-height: 24px;
          }
        }
      }

      // 分办表单样式
      .assignment-form-content {
        :deep(.el-form) {
          .el-form-item {
            .el-select {
              width: 100%;

              &__wrapper {
                width: 100%;
                height: 40px;
              }
            }

            .assignment-opinion-input {
              .el-textarea__inner {
                border-radius: 8px;
                font-size: 16px;
                color: #2B2C33;
                line-height: 24px;
                resize: none;

                &:focus {
                  border-color: #0EC3ED;
                  box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
                }

                &::placeholder {
                  color: #94959C;
                }
              }
            }
          }
        }
      }

      // 审核表单样式
      .review-form-content {
        :deep(.el-form) {
          .el-form-item {

            .el-select {
              width: 100%;

              &__wrapper {
                width: 100%;
                height: 40px;
              }
            }
          }
        }
      }

      // 省厅督办意见样式
      .supervision-content {
        padding: 16px;
        background: #f8f9fa;
        border-radius: 8px;
        border: 1px solid #e2e3e6;

        .supervision-header {
          display: grid;
          grid-template-columns: 1fr 1fr;
          gap: 24px;

          .supervision-opinion {
            flex: 1;

            .label {
              font-size: 14px;
              color: #6D6F75;
            }

            .description-text {
              font-size: 16px;
              color: #2B2C33;
              line-height: 24px;
            }
          }

          .supervision-time {

            .label {
              font-size: 14px;
              color: #6D6F75;
            }

            .time-text {
              font-size: 16px;
              color: #2B2C33;
              line-height: 24px;
            }
          }
        }

        .attachments {
          .label {
            font-size: 14px;
            color: #6D6F75;
          }

          .attachment-list {
            display: flex;
            justify-content: flex-start;
            align-items: center;
            flex-wrap: wrap;
            gap: 12px;

            .attachment-item {
              display: flex;
              justify-content: flex-start;
              align-items: center;
              cursor: pointer;
              transition: all 0.3s ease;
              background: #ffffff;
              border-radius: 8px 8px 8px 8px;
              border: 1px solid #e2e3e6;
              padding: 8px 13px;

              .attachment-icon {
                width: 15px;
                height: 16px;
                margin-right: 11px;
              }

              .attachment-name {
                font-size: 16px;
                color: #2b2c33;
                flex: 1;
              }

              .download-icon {
                margin-left: 11px;
              }
            }
          }
        }
      }

      // 回复表单样式
      .reply-form-content {
        // 回复表单相关样式可以在这里添加
      }
    }
  }
}


:deep(.el-form) {
  .el-form-item {
    margin-bottom: 0;

    .el-form-item__label {
      font-size: 14px;
      font-weight: 600;
      color: #2B2C33;
      margin-bottom: 8px;

      .attachment-label {
        .attachment-hint {
          font-weight: 400;
          color: #94959C;
        }
      }

      .assignment-opinion-label {
        .assignment-opinion-hint {
          font-weight: 400;
          color: #94959C;
        }
      }
    }

    .reply-textarea {
      .el-textarea__inner {
        border-radius: 8px;
        font-size: 16px;
        color: #2B2C33;
        line-height: 24px;
        resize: none;

        &:focus {
          border-color: #0EC3ED;
          box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
        }

        &::placeholder {
          color: #94959C;
        }
      }
    }

    .handler-input {
      .el-input__inner {
        border-radius: 8px;
        font-size: 16px;
        color: #2B2C33;
        height: 40px;

        &:focus {
          border-color: #0EC3ED;
          box-shadow: 0 0 0 2px rgba(14, 195, 237, 0.1);
        }

        &::placeholder {
          color: #94959C;
        }
      }
    }
  }

  .upload-area {
    width: 100%;

    .custom-upload-dragger {
      background: #ffffff;
      border: 1px dashed #E2E3E6;
      border-radius: 8px;
      width: 100%;
      height: 126px;
      display: flex;
      align-items: center;
      justify-content: center;
      transition: all 0.3s ease;
      cursor: pointer;

      &:hover,
      &.drag-over {
        border-color: #0EC3ED;
        background: #F8FCFF;
      }

      .upload-content {
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
        height: 100%;
        text-align: center;
        pointer-events: none;

        .el-icon {
          width: 33px;
          height: 24px;
        }

        .upload-text {
          font-size: 16px;
          color: #2B2C33;
          line-height: 24px;
          margin-top: 6px;
        }

        .upload-hint {
          font-size: 14px;
          color: #94959C;
          line-height: 20px;
        }
      }
    }

    .file-list {
      margin-top: 16px;

      .file-item {
        display: flex;
        align-items: center;
        padding: 8px 12px;
        background: #ffffff;
        border: 1px solid #E2E3E6;
        border-radius: 6px;
        margin-bottom: 8px;
        transition: all 0.3s ease;

        &:hover {
          background: #F8FCFF;
          border-color: #0EC3ED;
        }

        .file-icon {
          width: 20px;
          height: 20px;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .file-name {
          font-size: 14px;
          color: #2B2C33;
          flex: 1;
          margin-right: 8px;
          overflow: hidden;
          text-overflow: ellipsis;
          white-space: nowrap;
        }

        .file-size {
          font-size: 12px;
          color: #94959C;
          margin-right: 12px;
          flex-shrink: 0;
        }

        .remove-icon {
          font-size: 16px;
          color: #94959C;
          cursor: pointer;
          flex-shrink: 0;
          transition: color 0.3s ease;

          &:hover {
            color: #E72C4A;
          }
        }
      }
    }
  }
}
</style>
